"""
Configuration settings for XProtect detection system.
Contains all camera, API, and detection settings.
"""

# Dahua Camera Configuration
DAHUA_CONFIG = {
    "ip": "***************",
    "user": "admin",
    "password": "adminrs123",
    "channel": 1
}

# Blue Iris Configuration
BLUEIRIS_CONFIG = {
    "ip": "***************",
    "port": 81,
    "camera_short_name": "Cam1",
    "user": "admin",
    "password": "adminrs123"
}

# OpenAI Configuration
OPENAI_CONFIG = {
    "api_key": "********************************************************************************************************************************************************************",
    "model": "gpt-4o",
    "max_tokens": 300
}

# Detection Settings
DETECTION_CONFIG = {
    "interval": 5,  # seconds between checks
    "image_path": "snapshot.jpg",
    "prompt": (
        "You are a security AI. Analyze the image and tell me if there is any rubbish or litter on the floor. "
        "If you detect rubbish or someone littering, give a clear statement of what has been detected "
        "(e.g., 'A person throwing a plastic bottle on the floor' or 'Rubbish is present on the floor'). "
        "If the floor is clean, say 'No rubbish detected on the floor.' "
        "Focus only on detecting rubbish or the act of littering."
    )
}

# Derived URLs (computed from config)
def get_snapshot_url():
    """Get the snapshot URL for the Dahua camera."""
    return f"http://{DAHUA_CONFIG['ip']}/cgi-bin/snapshot.cgi?channel={DAHUA_CONFIG['channel']}"

def get_blueiris_url():
    """Get the Blue Iris admin URL."""
    return f"http://{BLUEIRIS_CONFIG['ip']}:{BLUEIRIS_CONFIG['port']}/admin"