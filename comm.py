"""
Communication module for Blue Iris integration.
Handles triggering alerts and other Blue Iris interactions.
"""

import requests
from config import BLUEIRIS_CONFIG, get_blueiris_url


def trigger_blueiris_alert(memo):
    """
    Trigger an alert in Blue Iris with the given memo.

    Args:
        memo (str): The memo/message to include with the alert

    Returns:
        bool: True if alert was successfully triggered, False otherwise
    """
    url = get_blueiris_url()
    params = {
        "camera": BLUEIRIS_CONFIG["camera_short_name"],
        "trigger": "",
        "user": BLUEIRIS_CONFIG["user"],
        "pw": BLUEIRIS_CONFIG["password"],
        "memo": memo
    }

    try:
        resp = requests.get(url, params=params, timeout=5)
        if resp.status_code == 200 and resp.text.strip() == "OK":
            print("[ALERT] Alert triggered with memo:", memo)
            return True
        else:
            print(f"[ALERT] Failed to trigger Blue Iris alert. Status: {resp.status_code}\n{resp.text}")
            return False
    except Exception as e:
        print("[ALERT] Error triggering Blue Iris alert:", e)
        return False


def test_blueiris_connection():
    """
    Test the connection to Blue Iris server.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        url = get_blueiris_url()
        params = {
            "user": BLUEIRIS_CONFIG["user"],
            "pw": BLUEIRIS_CONFIG["password"]
        }
        resp = requests.get(url, params=params, timeout=5)
        if resp.status_code == 200:
            print("[COMM] Blue Iris connection test successful")
            return True
        else:
            print(f"[COMM] Blue Iris connection test failed. Status: {resp.status_code}")
            return False
    except Exception as e:
        print(f"[COMM] Blue Iris connection test error: {e}")
        return False