import requests
import time
import base64
from requests.auth import HTTPDigestAuth
import openai

# Dahua Camera
dahua_ip = "***************"
dahua_user = "admin"
dahua_pw = "adminrs123"
channel = 1
snapshot_url = f"http://{dahua_ip}/cgi-bin/snapshot.cgi?channel={channel}"
image_path = "snapshot.jpg"

# Blue Iris
blueiris_ip = "***************"
blueiris_port = 81
camera_short_name = "Cam1"
bi_user = "admin"
bi_pw = "adminrs123"

# OpenAI GPT-4o (Vision) -- PuriCity key
openai.api_key = "********************************************************************************************************************************************************************"

# Detection prompt
prompt = (
    "You are a security AI. Analyze the image and tell me if there is any rubbish or litter on the floor. "
    "If you detect rubbish or someone littering, give a clear statement of what has been detected "
    "(e.g., 'A person throwing a plastic bottle on the floor' or 'Rubbish is present on the floor'). "
    "If the floor is clean, say 'No rubbish detected on the floor.' "
    "Focus only on detecting rubbish or the act of littering."
)

interval = 5  # seconds

def capture_snapshot():
    try:
        resp = requests.get(snapshot_url, auth=HTTPDigestAuth(dahua_user, dahua_pw), stream=True, timeout=8)
        if resp.status_code == 200 and resp.headers.get('content-type', '').startswith("image"):
            with open(image_path, "wb") as f:
                for chunk in resp.iter_content(chunk_size=8192):
                    f.write(chunk)
            return True
        elif resp.status_code == 403 and ("User account is locked" in resp.text or "ErrorCode" in resp.text):
            print("[WARN] Dahua user account is locked. Waiting 30 seconds before retrying...")
            time.sleep(30)
            return False
        else:
            print(f"[ERROR] Snapshot failed. Status: {resp.status_code}")
            print("Response:", resp.text)
            time.sleep(2)
            return False
    except Exception as e:
        print(f"[ERROR] Snapshot exception: {e}")
        time.sleep(2)
        return False

def analyze_with_openai():
    with open(image_path, "rb") as image_file:
        img_bytes = image_file.read()
        image_base64 = base64.b64encode(img_bytes).decode("utf-8")
    try:
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are an AI security assistant."},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                    ]
                }
            ],
            max_tokens=300
        )
        output_text = response.choices[0].message.content.strip()
        print("\n[OpenAI Vision Output]:", output_text)
        output_lower = output_text.lower()
        detected = (
            ("rubbish" in output_lower or "litter" in output_lower)
            and not output_lower.startswith("no rubbish detected")
            and not output_lower.startswith("no litter detected")
            and "no rubbish" not in output_lower
            and "no litter" not in output_lower
        )
        return output_text, detected
    except Exception as e:
        print("[ERROR] OpenAI API Error:", e)
        return f"Error: {e}", False

def trigger_blueiris_alert(memo):
    url = f"http://{blueiris_ip}:{blueiris_port}/admin"
    params = {
        "camera": camera_short_name,
        "trigger": "",
        "user": bi_user,
        "pw": bi_pw,
        "memo": memo
    }
    try:
        resp = requests.get(url, params=params, timeout=5)
        if resp.status_code == 200 and resp.text.strip() == "OK":
            print("[ALERT] Alert triggered with memo:", memo)
        else:
            print(f"[ALERT] Failed to trigger Blue Iris alert. Status: {resp.status_code}\n{resp.text}")
    except Exception as e:
        print("[ALERT] Error triggering Blue Iris alert:", e)

def monitor_loop():
    print("===== Dahua → OpenAI → Blue Iris Rubbish Detection STARTED =====")
    while True:
        print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] Checking for rubbish/litter...")
        if capture_snapshot():
            result_text, detected = analyze_with_openai()
            if detected:
                print("[AI DETECTION] Rubbish/litter detected! Triggering Blue Iris alert.")
                trigger_blueiris_alert(result_text)
            else:
                print("[AI DETECTION] No rubbish/litter detected. No alert.")
        else:
            print("[INFO] Snapshot not available, will retry.")
        time.sleep(interval)

if __name__ == "__main__":
    monitor_loop()
