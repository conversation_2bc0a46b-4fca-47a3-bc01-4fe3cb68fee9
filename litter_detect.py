import requests
import time
import base64
from requests.auth import HTTPDigestAuth
import openai

# Import configuration and communication modules
from config import (
    DAHUA_CONFIG,
    OPENAI_CONFIG,
    DETECTION_CONFIG,
    get_snapshot_url
)
from comm import trigger_blueiris_alert

# Set up OpenAI API key
openai.api_key = OPENAI_CONFIG["api_key"]

def capture_snapshot():
    try:
        snapshot_url = get_snapshot_url()
        resp = requests.get(
            snapshot_url,
            auth=HTTPDigestAuth(DAHUA_CONFIG["user"], DAHUA_CONFIG["password"]),
            stream=True,
            timeout=8
        )
        if resp.status_code == 200 and resp.headers.get('content-type', '').startswith("image"):
            with open(DETECTION_CONFIG["image_path"], "wb") as f:
                for chunk in resp.iter_content(chunk_size=8192):
                    f.write(chunk)
            return True
        elif resp.status_code == 403 and ("User account is locked" in resp.text or "ErrorCode" in resp.text):
            print("[WARN] Dahua user account is locked. Waiting 30 seconds before retrying...")
            time.sleep(30)
            return False
        else:
            print(f"[ERROR] Snapshot failed. Status: {resp.status_code}")
            print("Response:", resp.text)
            time.sleep(2)
            return False
    except Exception as e:
        print(f"[ERROR] Snapshot exception: {e}")
        time.sleep(2)
        return False

def analyze_with_openai():
    with open(DETECTION_CONFIG["image_path"], "rb") as image_file:
        img_bytes = image_file.read()
        image_base64 = base64.b64encode(img_bytes).decode("utf-8")
    try:
        response = openai.chat.completions.create(
            model=OPENAI_CONFIG["model"],
            messages=[
                {"role": "system", "content": "You are an AI security assistant."},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": DETECTION_CONFIG["prompt"]},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                    ]
                }
            ],
            max_tokens=OPENAI_CONFIG["max_tokens"]
        )
        output_text = response.choices[0].message.content.strip()
        print("\n[OpenAI Vision Output]:", output_text)
        output_lower = output_text.lower()
        detected = (
            ("rubbish" in output_lower or "litter" in output_lower)
            and not output_lower.startswith("no rubbish detected")
            and not output_lower.startswith("no litter detected")
            and "no rubbish" not in output_lower
            and "no litter" not in output_lower
        )
        return output_text, detected
    except Exception as e:
        print("[ERROR] OpenAI API Error:", e)
        return f"Error: {e}", False

def monitor_loop():
    print("===== Dahua → OpenAI → Blue Iris Rubbish Detection STARTED =====")
    while True:
        print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] Checking for rubbish/litter...")
        if capture_snapshot():
            result_text, detected = analyze_with_openai()
            if detected:
                print("[AI DETECTION] Rubbish/litter detected! Triggering Blue Iris alert.")
                trigger_blueiris_alert(result_text)
            else:
                print("[AI DETECTION] No rubbish/litter detected. No alert.")
        else:
            print("[INFO] Snapshot not available, will retry.")
        time.sleep(DETECTION_CONFIG["interval"])

if __name__ == "__main__":
    monitor_loop()
